{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-06-17T20:43:55.435Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-06-17T20:43:55.483Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-06-17T20:43:55.519Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.520Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.520Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.521Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.522Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.522Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-06-17T20:43:55.663Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-06-17T20:43:55.663Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"Block tracking started","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-06-17T20:43:55.665Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.197Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.198Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.198Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.200Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.200Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.200Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.201Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.201Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.201Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.202Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.202Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.202Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.202Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.203Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.203Z"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:43:56.269Z"}
{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-06-17T20:49:08.057Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-06-17T20:49:08.099Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:49:08.134Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:49:08.134Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-06-17T20:49:08.135Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:49:08.136Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:49:08.136Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:49:08.137Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-06-17T20:49:08.262Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-06-17T20:49:08.263Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-06-17T20:49:08.263Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-06-17T20:49:08.263Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-06-17T20:49:08.263Z"}
{"level":"info","message":"Block tracking started","timestamp":"2025-06-17T20:49:08.264Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-06-17T20:49:08.264Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:49:09.219Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC","USDT","DAI"],"timestamp":"2025-06-17T20:49:09.219Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC, USDT, DAI","timestamp":"2025-06-17T20:49:09.219Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:49:09.221Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC","USDT","DAI"],"timestamp":"2025-06-17T20:49:09.221Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC, USDT, DAI","timestamp":"2025-06-17T20:49:09.222Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:49:09.222Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC","USDT","DAI"],"timestamp":"2025-06-17T20:49:09.222Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC, USDT, DAI","timestamp":"2025-06-17T20:49:09.223Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:49:09.223Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC","USDT","DAI"],"timestamp":"2025-06-17T20:49:09.223Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC, USDT, DAI","timestamp":"2025-06-17T20:49:09.224Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:49:09.224Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC","USDT","DAI"],"timestamp":"2025-06-17T20:49:09.224Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC, USDT, DAI","timestamp":"2025-06-17T20:49:09.225Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:49:09.225Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC","USDT","DAI"],"timestamp":"2025-06-17T20:49:09.225Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC, USDT, DAI","timestamp":"2025-06-17T20:49:09.226Z"}
{"level":"debug","liquidityUsd":20.052196486014285,"message":"V2 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.257Z","tokenA":"WETH","tokenB":"USDC"}
{"level":"debug","liquidityUsd":20.052196486014285,"message":"V2 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.257Z","tokenA":"WETH","tokenB":"USDC"}
{"level":"debug","liquidityUsd":20.052196486014285,"message":"V2 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.257Z","tokenA":"WETH","tokenB":"USDC"}
{"level":"debug","liquidityUsd":20.052196486014285,"message":"V2 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.258Z","tokenA":"WETH","tokenB":"USDC"}
{"level":"debug","liquidityUsd":20.052196486014285,"message":"V2 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.258Z","tokenA":"WETH","tokenB":"USDC"}
{"level":"debug","liquidityUsd":20.052196486014285,"message":"V2 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.258Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"debug","liquidityUsd":20.052196486014285,"message":"Pool found for DEX","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.258Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"warn","liquidityUsd":20.052196486014285,"message":"Pool has very low liquidity","suggestion":"Consider using different token pairs or adding liquidity to testnet","timestamp":"2025-06-17T20:49:09.258Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"debug","liquidityUsd":20.052196486014285,"message":"Pool found for DEX","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.258Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"warn","liquidityUsd":20.052196486014285,"message":"Pool has very low liquidity","suggestion":"Consider using different token pairs or adding liquidity to testnet","timestamp":"2025-06-17T20:49:09.259Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"debug","liquidityUsd":20.052196486014285,"message":"Pool found for DEX","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.259Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"warn","liquidityUsd":20.052196486014285,"message":"Pool has very low liquidity","suggestion":"Consider using different token pairs or adding liquidity to testnet","timestamp":"2025-06-17T20:49:09.259Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"debug","liquidityUsd":20.052196486014285,"message":"Pool found for DEX","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.259Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"warn","liquidityUsd":20.052196486014285,"message":"Pool has very low liquidity","suggestion":"Consider using different token pairs or adding liquidity to testnet","timestamp":"2025-06-17T20:49:09.259Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"debug","liquidityUsd":20.052196486014285,"message":"Pool found for DEX","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.259Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"warn","liquidityUsd":20.052196486014285,"message":"Pool has very low liquidity","suggestion":"Consider using different token pairs or adding liquidity to testnet","timestamp":"2025-06-17T20:49:09.259Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"debug","liquidityUsd":20.052196486014285,"message":"Pool found for DEX","poolAddress":"******************************************","protocol":"uniswap-v2","timestamp":"2025-06-17T20:49:09.260Z","tokenA":"WETH","tokenB":"USDC"}
{"dex":"UNISWAP_V2","level":"warn","liquidityUsd":20.052196486014285,"message":"Pool has very low liquidity","suggestion":"Consider using different token pairs or adding liquidity to testnet","timestamp":"2025-06-17T20:49:09.260Z","tokenA":"WETH","tokenB":"USDC"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.299Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:49:09.299Z"}
{"calculation":"10^12 / rawPrice","finalPrice":182370.74676958338,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5483335.555254658,"tick":"155180","tickNumber":155180,"timestamp":"2025-06-17T20:49:09.299Z"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"Found better pool with fee tier","poolAddress":"******************************************","price":182370.74676958338,"protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.300Z","tokenA":"WETH","tokenB":"USDC"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.300Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:49:09.300Z"}
{"calculation":"10^12 / rawPrice","finalPrice":182370.74676958338,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5483335.555254658,"tick":"155180","tickNumber":155180,"timestamp":"2025-06-17T20:49:09.300Z"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"Found better pool with fee tier","poolAddress":"******************************************","price":182370.74676958338,"protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.301Z","tokenA":"WETH","tokenB":"USDC"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.301Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:49:09.301Z"}
{"calculation":"10^12 / rawPrice","finalPrice":182370.74676958338,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5483335.555254658,"tick":"155180","tickNumber":155180,"timestamp":"2025-06-17T20:49:09.301Z"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"Found better pool with fee tier","poolAddress":"******************************************","price":182370.74676958338,"protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.301Z","tokenA":"WETH","tokenB":"USDC"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.302Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:49:09.302Z"}
{"calculation":"10^12 / rawPrice","finalPrice":182370.74676958338,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5483335.555254658,"tick":"155180","tickNumber":155180,"timestamp":"2025-06-17T20:49:09.302Z"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"Found better pool with fee tier","poolAddress":"******************************************","price":182370.74676958338,"protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.302Z","tokenA":"WETH","tokenB":"USDC"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.303Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:49:09.303Z"}
{"calculation":"10^12 / rawPrice","finalPrice":182370.74676958338,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5483335.555254658,"tick":"155180","tickNumber":155180,"timestamp":"2025-06-17T20:49:09.303Z"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"Found better pool with fee tier","poolAddress":"******************************************","price":182370.74676958338,"protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.303Z","tokenA":"WETH","tokenB":"USDC"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.304Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:49:09.304Z"}
{"calculation":"10^12 / rawPrice","finalPrice":182370.74676958338,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5483335.555254658,"tick":"155180","tickNumber":155180,"timestamp":"2025-06-17T20:49:09.304Z"}
{"fee":500,"level":"debug","liquidityUsd":1000,"message":"Found better pool with fee tier","poolAddress":"******************************************","price":182370.74676958338,"protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.304Z","tokenA":"WETH","tokenB":"USDC"}
{"fee":3000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.342Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155145","timestamp":"2025-06-17T20:49:09.342Z"}
{"calculation":"10^12 / rawPrice","finalPrice":183010.13068379217,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5464178.383260192,"tick":"155145","tickNumber":155145,"timestamp":"2025-06-17T20:49:09.342Z"}
{"fee":3000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.343Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155145","timestamp":"2025-06-17T20:49:09.343Z"}
{"calculation":"10^12 / rawPrice","finalPrice":183010.13068379217,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5464178.383260192,"tick":"155145","tickNumber":155145,"timestamp":"2025-06-17T20:49:09.344Z"}
{"fee":3000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.344Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155145","timestamp":"2025-06-17T20:49:09.344Z"}
{"calculation":"10^12 / rawPrice","finalPrice":183010.13068379217,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5464178.383260192,"tick":"155145","tickNumber":155145,"timestamp":"2025-06-17T20:49:09.344Z"}
{"fee":3000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.345Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155145","timestamp":"2025-06-17T20:49:09.345Z"}
{"calculation":"10^12 / rawPrice","finalPrice":183010.13068379217,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5464178.383260192,"tick":"155145","tickNumber":155145,"timestamp":"2025-06-17T20:49:09.345Z"}
{"fee":3000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.346Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155145","timestamp":"2025-06-17T20:49:09.346Z"}
{"calculation":"10^12 / rawPrice","finalPrice":183010.13068379217,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5464178.383260192,"tick":"155145","tickNumber":155145,"timestamp":"2025-06-17T20:49:09.346Z"}
{"fee":3000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.346Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155145","timestamp":"2025-06-17T20:49:09.346Z"}
{"calculation":"10^12 / rawPrice","finalPrice":183010.13068379217,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5464178.383260192,"tick":"155145","tickNumber":155145,"timestamp":"2025-06-17T20:49:09.346Z"}
{"fee":10000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.380Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155084","timestamp":"2025-06-17T20:49:09.381Z"}
{"calculation":"10^12 / rawPrice","finalPrice":184129.84816245065,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5430950.006094279,"tick":"155084","tickNumber":155084,"timestamp":"2025-06-17T20:49:09.381Z"}
{"fee":10000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.382Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155084","timestamp":"2025-06-17T20:49:09.382Z"}
{"calculation":"10^12 / rawPrice","finalPrice":184129.84816245065,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5430950.006094279,"tick":"155084","tickNumber":155084,"timestamp":"2025-06-17T20:49:09.382Z"}
{"fee":10000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.383Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155084","timestamp":"2025-06-17T20:49:09.383Z"}
{"calculation":"10^12 / rawPrice","finalPrice":184129.84816245065,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5430950.006094279,"tick":"155084","tickNumber":155084,"timestamp":"2025-06-17T20:49:09.383Z"}
{"fee":10000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.383Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155084","timestamp":"2025-06-17T20:49:09.383Z"}
{"calculation":"10^12 / rawPrice","finalPrice":184129.84816245065,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5430950.006094279,"tick":"155084","tickNumber":155084,"timestamp":"2025-06-17T20:49:09.383Z"}
{"fee":10000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.384Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155084","timestamp":"2025-06-17T20:49:09.384Z"}
{"calculation":"10^12 / rawPrice","finalPrice":184129.84816245065,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5430950.006094279,"tick":"155084","tickNumber":155084,"timestamp":"2025-06-17T20:49:09.384Z"}
{"fee":10000,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.384Z","tokenA":"WETH","tokenB":"USDC"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155084","timestamp":"2025-06-17T20:49:09.385Z"}
{"calculation":"10^12 / rawPrice","finalPrice":184129.84816245065,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"V3 price calculated (real tick calculation)","poolToken0":"USDC","poolToken1":"WETH","rawPrice":5430950.006094279,"tick":"155084","tickNumber":155084,"timestamp":"2025-06-17T20:49:09.385Z"}
{"fee":100,"level":"debug","liquidityUsd":1000,"message":"V3 pool liquidity check","poolAddress":"******************************************","protocol":"uniswap-v3","timestamp":"2025-06-17T20:49:09.417Z","tokenA":"WETH","tokenB":"USDC"}
